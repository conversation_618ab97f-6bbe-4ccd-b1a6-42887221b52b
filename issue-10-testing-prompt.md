# Issue #10: Rush Processing Submission - Testing Prompt

## Agent Instructions

**You are tasked with testing the rush processing submission functionality implementation.** Your goal is to run the core-agent testing scripts and analyze the results to determine if the implementation is working correctly.

## Context

The `RequestRushProcessingHandler` in `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts` has been enhanced to include actual submission logic. Previously, it only sent backoffice alerts. Now it:

1. Attempts to submit shipments through the established submission workflow
2. Uses `CustomStatusService.processShipmentForCustomsStatus()` for submission
3. Includes proper transaction management with database rollback on failures
4. Updates the template to display submission results
5. Maintains existing alert functionality as a fallback

## Testing Requirements

### 1. Prepare for Testing

**CRITICAL**: Templates must be compiled before testing:

```bash
# Always run build after template changes
cd apps/portal-api
rushx build

# Or from project root
rush build --to portal-api
```

### 2. Test the Rush Processing Handler

Use the core-agent testing scripts to test the REQUEST_RUSH_PROCESSING intent:

```bash
# Navigate to portal-api directory
cd apps/portal-api

# Test with automatic logging (preferred method)
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=REQUEST_RUSH_PROCESSING --verbose

# Test with specific shipment that can be submitted
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=123 --verbose

# Test with no side effects (development safe)
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --no-side-effects --verbose
```

### 3. Find Suitable Test Shipments

Use the shipment discovery script to find shipments in various states:

```bash
# Find shipments with different statuses for testing
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments that can be submitted (pending-arrival, live)
node src/core-agent/testing/find-test-shipments.js --status=pending-arrival
node src/core-agent/testing/find-test-shipments.js --status=live

# Find shipments already submitted (should skip submission)
node src/core-agent/testing/find-test-shipments.js --status=entry-submitted
node src/core-agent/testing/find-test-shipments.js --status=released
```

### 4. Test Different Shipment Scenarios

Test the handler with shipments in different states:

```bash
# Test with shipment that should trigger submission
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=[pending-arrival-shipment-id] --verbose

# Test with already submitted shipment (should skip submission)
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=[entry-submitted-shipment-id] --verbose

# Test with shipment having compliance issues
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING --shipment=[pending-confirmation-shipment-id] --verbose
```

### 5. Test End-to-End Email Processing

Test the complete pipeline with rush processing emails:

```bash
# Test complete email pipeline with rush processing
./src/core-agent/testing/run-e2e-with-logs.sh --body="Please rush this shipment" --subject="Rush Processing Test"

# Test with specific organization
node src/core-agent/testing/e2e-email-pipeline-nestjs.js --org=3 --body="We need this expedited"
```

### 6. Database Verification

Use the database helper to verify submission results:

```bash
# Check recent emails for rush processing
node src/core-agent/testing/db-query.js sql "SELECT id, subject, status, \"organizationId\" FROM email WHERE subject LIKE '%rush%' ORDER BY \"createDate\" DESC LIMIT 5;"

# Check shipment status updates
node src/core-agent/testing/db-query.js sql "SELECT id, \"hblNumber\", \"customsStatus\", \"trackingStatus\" FROM shipment WHERE \"organizationId\" = 3 ORDER BY \"updateDate\" DESC LIMIT 5;"
```

## Success Criteria

### ✅ Tests Pass If:

1. **Handler Executes Successfully**: REQUEST_RUSH_PROCESSING intent runs without errors
2. **Submission Logic Works**: Appropriate shipments trigger submission workflow
3. **Status Filtering**: Already-submitted shipments skip submission appropriately
4. **Template Rendering**: Enhanced template displays submission results correctly
5. **Database Integrity**: Transactions commit/rollback properly without corruption
6. **Error Handling**: Submission failures don't break email processing flow

### ❌ Tests Fail If:

1. **Handler Crashes**: Unhandled exceptions during REQUEST_RUSH_PROCESSING
2. **Service Injection Errors**: Missing CustomStatusService, ComplianceValidationService, or DataSource
3. **Transaction Failures**: Database deadlocks, connection leaks, or rollback issues
4. **Template Errors**: Missing templates, compilation errors, or context variable issues
5. **Infinite Loops**: Improper status checks causing repeated submissions
6. **Memory Leaks**: QueryRunner not properly released in finally blocks

## Key Log Indicators

### Look for these SUCCESS indicators:
- `"Attempting submission workflow for shipment X with status Y"`
- `"Rush processing submission completed for shipment X"`
- `"Shipment X is already in final status Y, skipping submission"`
- Handler completes with fragments returned
- Template context includes `submissionResult` or `submissionError`
- Database transactions commit successfully
- Response fragments include enhanced template content

### Look for these FAILURE indicators:
- `"Failed to process shipment for customs status"`
- `"Error during submission workflow"`
- `"No validation results for shipment"`
- `"Cannot find template"` or template compilation errors
- Handler crashes with unhandled exceptions
- Database connection timeouts or transaction rollbacks
- Missing service dependencies during injection

## Expected Behavior by Shipment Status

### Shipments That Should Skip Submission:
- `entry-submitted` - Already submitted, should skip
- `entry-accepted` - Already accepted, should skip  
- `exam` - In examination, should skip
- `released` - Already released, should skip

**Expected Log**: `"Shipment X is already in final status Y, skipping submission"`

### Shipments That Should Attempt Submission:
- `pending-arrival` - Ready for submission
- `live` - Active shipment, can be submitted
- `pending-confirmation` - Has compliance issues but should attempt

**Expected Log**: `"Attempting submission workflow for shipment X with status Y"`

### Shipments With Compliance Issues:
- `pending-commercial-invoice` - Missing documents
- `pending-confirmation` - Missing required fields

**Expected Behavior**: Should attempt submission but may fail with structured error result

## Testing Instructions

1. **Start with Build**: Always run `rushx build` from `apps/portal-api` first
2. **Find Test Shipments**: Use `find-test-shipments.js` to identify suitable shipments
3. **Test Handler Logic**: Use `test-intent-handlers.js` with `REQUEST_RUSH_PROCESSING`
4. **Test E2E Pipeline**: Use `e2e-email-pipeline-nestjs.js` with rush processing emails
5. **Check Database**: Use `db-query.js` to verify submission results
6. **Analyze Logs**: Check processor logs in `.ai-reference/` directory

## Report Format

Please provide:
1. **Pre-test Setup**: Build status and template compilation results
2. **Handler Testing**: Direct handler test results with different shipment statuses
3. **E2E Testing**: Complete email pipeline test results
4. **Database Verification**: Shipment status changes and transaction integrity
5. **Performance Analysis**: Response times and any bottlenecks
6. **Error Analysis**: Detailed analysis of any failures
7. **Recommendations**: Suggestions for any issues found

## Important Notes

- The implementation follows the established `ProcessDocumentHandler` pattern
- Database transactions are critical for data consistency
- Template enhancements should be backwards compatible
- Error handling should not break the email processing flow
- Service injection follows NestJS dependency injection patterns

Run the tests and report your findings. Focus on functional correctness, error handling, and integration with existing systems.