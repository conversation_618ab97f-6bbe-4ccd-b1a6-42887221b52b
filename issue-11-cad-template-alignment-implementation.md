# Issue #11: CAD Template Alignment - Implementation Plan

## Agent Instructions

**You are tasked with fixing CAD template messaging to eliminate false promises.** Be skeptical of the analysis provided and verify all claims by examining the actual codebase. The goal is to ensure the CAD response template provides accurate messaging about CAD document availability without breaking the established architecture.

## Problem Summary

**Current Issue**: The `send-cad-response.njk` template promises CAD documents for shipment statuses where they won't be generated, creating false expectations. However, the underlying CAD generation logic is already correctly aligned with business rules.

**Business Impact**: Users expect CAD documents that will never be delivered, leading to follow-up inquiries and reduced confidence in the system.

## Detailed Problem Analysis

### Architecture Understanding (CRITICAL)

**The CAD system uses a side effects pattern**:
1. `ShipmentContextService` sets `cadDocumentAvailable` using `isSendCADReady()`
2. `RequestCADDocumentHand<PERSON>` checks this flag and generates CAD as side effect
3. Template displays message based on whether CAD was actually generated
4. Email processor attaches CAD document from side effects

**Key Context Variables**:
- `context.canGenerateCAD`: Business rule evaluation (already aligned with `isSendCADReady()`)
- `context.smartTemplateContext.cadDocumentAvailable`: Template-specific flag (already aligned)
- `cadDocument`: Present in fragment context when CAD was actually generated

### Current Template Messaging Issues

**Template Falsely Promises CAD For**:
- `pending-arrival`: "We have notified our team to send you the CAD document"
- `live`: "We have notified our team to send you the CAD document"
- `entry-submitted`: "We have notified our team to send you the CAD document"

**Reality**: CAD is only generated when `isSendCADReady()` returns true:
```typescript
// From apps/portal-api/src/core-agent/constants/customs-definitions.constants.ts
export function isSendCADReady(status: string | null | undefined): boolean {
  return [
    CustomsStatus.ENTRY_ACCEPTED,
    CustomsStatus.EXAM,
    CustomsStatus.RELEASED,
    CustomsStatus.ACCOUNTING_COMPLETED
  ].includes(status as CustomsStatus);
}
```

### Missing Context Display

**Additional Issue**: Templates don't display transaction numbers when available, which is important context for users.

## Implementation Strategy

### Phase 1: Fix Template Messaging (Not Logic)

**File to Modify**: `apps/portal-api/src/core-agent/templates/core-agent/fragments/send-cad-response.njk`

**CRITICAL**: Do not modify CAD generation logic - it's already correctly aligned. Only fix the messaging.

**Current Problematic Sections**:
```njk
{% elif shipment.customsStatus == 'pending-arrival' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'live' %}
We have notified our team to send you the CAD document. Thank you for your patience.

{% elif shipment.customsStatus == 'entry-submitted' %}
We have notified our team to send you the CAD document. Thank you for your patience.
```

**Correct Approach - Use Side Effects Pattern**:
```njk
{% if cadDocument %}
Please see CAD document attached.
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'pending-arrival' %}
The CAD document will be available once the customs entry is accepted. Current status: {{ formattedCustomsStatus }}.
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'live' %}
The customs entry is ready for submission. The CAD document will be available once accepted by customs.
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}

{% elif shipment.customsStatus == 'entry-submitted' %}
The customs entry has been submitted and is awaiting acceptance. The CAD document will be available once accepted.
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}

{% else %}
The CAD document is not yet available for this shipment status. Current status: {{ formattedCustomsStatus }}.
{% if shipment.transactionNumber %}
<br />Transaction Number: <strong>{{ shipment.transactionNumber }}</strong>
{% endif %}
{% endif %}
```

### Phase 2: Verify Context Alignment (Already Correct)

**Verify these are already properly set**:
```typescript
// In ShipmentContextService.buildSmartTemplateContext()
cadDocumentAvailable: this.ruleEvaluator.canGenerateCAD(shipment.customsStatus)

// In ShipmentServicesAdapter.canGenerateCAD()
canGenerateCAD(customsStatus: string): boolean {
  return isSendCADReady(customsStatus);
}
```

### Phase 3: Understand Handler Pattern (Do Not Modify)

**The RequestCADDocumentHandler already works correctly**:
```typescript
const cadData = context.smartTemplateContext.cadDocumentAvailable
  ? await this.generateCADDocument(context.shipment)
  : {};
```

**This generates `cadDocument` in fragment context when appropriate**.

## Critical Implementation Details

### Side Effects Architecture (CRITICAL - Do Not Modify)

**The CAD system uses established patterns**:
1. `RequestCADDocumentHandler` generates CAD as side effect when `cadDocumentAvailable` is true
2. CAD document is added to fragment context as `cadDocument`
3. Email processor collects side effects and attaches CAD to email
4. Template checks `cadDocument` presence to determine messaging

### Handler Integration (Already Correct)

**The `RequestCADDocumentHandler` already**:
- Checks `context.smartTemplateContext.cadDocumentAvailable`
- Generates CAD document when appropriate
- Adds `cadDocument` to fragment context
- Uses `buildComplianceDetails()` for consistent context

### Context Variables (Already Available)

**These variables are already properly provided**:
- `shipment.customsStatus`: Available in all contexts
- `shipment.transactionNumber`: Set in `smartTemplateContext`
- `formattedCustomsStatus`: Available from context formatting
- `cadDocument`: Present when CAD was generated as side effect

### Business Logic Verification (Already Correct)

**The alignment is already correct**:
- `isSendCADReady()` defines when CAD can be generated
- `cadDocumentAvailable` is set using `isSendCADReady()`
- Handler only generates CAD when `cadDocumentAvailable` is true
- **Problem is messaging, not logic**

## Testing Requirements

### Unit Tests Needed
- Test template rendering with and without `cadDocument` present
- Test template messaging for each customs status
- Test transaction number display when available/unavailable
- Verify template doesn't break existing CAD generation logic

### Integration Tests Needed
- Test CAD request flow with various shipment statuses
- Verify email content matches actual CAD attachment presence
- Test that CAD documents are still properly attached when generated
- Test user experience messaging for each status scenario

### Architecture Tests Needed
- Verify `RequestCADDocumentHandler` still generates CAD correctly
- Test side effects collection and email attachment process
- Confirm no regression in existing CAD functionality

## Skeptical Review Points

**Question these assumptions**:
1. Is the side effects pattern correctly understood and preserved?
2. Does the template correctly check `cadDocument` presence vs status?
3. Are all context variables actually available in the template?
4. Will this change break the existing CAD generation workflow?

**Verify by examining**:
- `RequestCADDocumentHandler` implementation and CAD generation logic
- How `cadDocument` is added to fragment context
- Email processor side effects collection (`collectSideEffectsFromFragments`)
- Current template context variables and their sources
- Existing CAD attachment functionality

## Success Criteria

1. **Accuracy**: Template messaging matches actual CAD document attachment behavior
2. **Clarity**: Users understand when CAD will be available and why
3. **Architecture Preservation**: Existing CAD generation and side effects patterns remain intact
4. **Informativeness**: Users see relevant status and transaction information
5. **Trust**: No false promises that erode user confidence

## Implementation Checklist

- [ ] Examine `RequestCADDocumentHandler` and understand side effects pattern
- [ ] Verify how `cadDocument` is added to fragment context
- [ ] Update template to check `cadDocument` presence first
- [ ] Add appropriate messaging for each status when CAD not available
- [ ] Implement transaction number display consistently
- [ ] Test template with CAD generation enabled and disabled
- [ ] Verify no regression in CAD attachment functionality
- [ ] Test email processor side effects collection
- [ ] Ensure template context variables are available
- [ ] Test user experience for each scenario

## Expected Outcome

Users will receive accurate messaging about CAD document availability that matches whether a CAD document was actually generated and attached to their email. The template will provide clear, status-appropriate messaging without breaking the established CAD generation architecture. Transaction numbers will be displayed when available to provide additional context. The existing side effects pattern for CAD generation will be preserved and work correctly.
