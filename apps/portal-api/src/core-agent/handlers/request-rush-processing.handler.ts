import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext, ShipmentContextWithServices } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { CustomStatusService } from "../../shipment/services/custom-status.service";
import { ComplianceValidationService } from "../../shipment/services/compliance-validation.service";
import { DataSource } from "typeorm";
import { Shipment } from "nest-modules";

/**
 * Handles requests for expedited/rush processing of shipments.
 * Evaluates business rules, attempts submission when appropriate, and sends backoffice alerts.
 * Follows the established submission workflow pattern for consistency.
 */
@Injectable()
export class RequestRushProcessingHandler extends BaseIntentHandler {
  constructor(
    private readonly emailService: EmailService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService,
    private readonly customStatusService: CustomStatusService,
    private readonly complianceValidationService: ComplianceValidationService,
    private readonly dataSource: DataSource
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RUSH_PROCESSING" as const,
    description: "User is requesting expedited or rush processing for their shipment",
    examples: [
      "Can you rush this shipment?",
      "We need this expedited",
      "Please prioritize this load",
      "This is urgent, can you speed it up?",
      "Rush processing needed"
    ],
    keywords: ["rush", "urgent", "expedite", "prioritize", "speed", "asap", "quickly", "fast"]
  };

  /**
   * Attempts to submit the shipment for rush processing following the established workflow.
   * Returns submission results for template context.
   */
  private async attemptSubmission(context: ShipmentContextWithServices): Promise<any> {
    const shipment = context.shipment;
    if (!shipment) {
      this.logger.error("No shipment found in context for submission");
      return { submissionError: "No shipment found for submission" };
    }

    // Check if shipment is already submitted (prevent unnecessary processing)
    const finalStatuses = ['entry-submitted', 'entry-accepted', 'exam', 'released'];
    if (finalStatuses.includes(shipment.customsStatus)) {
      this.logger.log(`Shipment ${shipment.id} is already in final status ${shipment.customsStatus}, skipping submission`);
      return {}; // Return empty object - the template will handle the status message
    }

    try {
      // Get compliance validation (required for submission)
      const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([shipment]);
      const validationResults = this.complianceValidationService.validateShipmentCompliances(
        shipmentCompliances,
        this.complianceValidationService.isDemoShipment(shipment)
      );

      if (validationResults.length === 0) {
        this.logger.error(`No validation results for shipment ${shipment.id}`);
        return { submissionError: "Unable to validate shipment for submission" };
      }

      const validationResult = validationResults[0];

      // Create transaction for submission (critical for data consistency)
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Use the established submission orchestrator
        const result = await this.customStatusService.processShipmentForCustomsStatus(
          shipment,
          validationResult,
          queryRunner
        );

        // Handle status updates (critical - sync in-memory object)
        if (result.shipmentStatusUpdate) {
          const shipmentRepository = queryRunner.manager.getRepository(Shipment);
          await shipmentRepository.update(
            { id: result.shipmentStatusUpdate.shipmentId },
            { customsStatus: result.shipmentStatusUpdate.newStatus }
          );
          shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
        }

        await queryRunner.commitTransaction();
        
        this.logger.log(`Rush processing submission completed for shipment ${shipment.id}`);
        return { submissionResult: result };

      } catch (error) {
        await queryRunner.rollbackTransaction();
        this.logger.error(`Failed to process shipment for customs status: ${error.message}`);
        return { submissionError: error.message };
      } finally {
        await queryRunner.release();
      }

    } catch (error) {
      this.logger.error(`Error during submission workflow: ${error.message}`);
      return { submissionError: error.message };
    }
  }

  async handle(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(`Handling REQUEST_RUSH_PROCESSING for shipment ${context.shipment?.id || "N/A"}`);

    if (instructions.length === 0) {
      this.logger.error("Cannot process rush processing request: no instructions provided");
      throw new Error("No instructions provided for rush processing request");
    }

    try {
      // Collect data from helper methods (no context mutation)
      const alertData = await this.sendBackofficeAlert(
        "Rush Processing Request",
        context.shipment?.id || 0,
        instructions,
        context._services.emailService,
        context.organization.id
      );
      const complianceData = this.shipmentResponseService.buildComplianceDetails(context);

      // Attempt submission workflow for rush processing
      this.logger.log(`Attempting submission workflow for shipment ${context.shipment?.id} with status ${context.shipment?.customsStatus}`);
      const submissionData = await this.attemptSubmission(context);

      // Rush processing response fragment (priority 1)
      fragments.push({
        template: "core-agent/fragments/document-requests/rush-processing-response",
        priority: 1,
        fragmentContext: {
          ...alertData, // { backofficeAlerts: { rushProcessingSent: true } }
          ...complianceData, // { complianceDetails: {...}, missingFieldsFormatted: "..." }
          ...submissionData, // { submissionResult: {...} } or { submissionError: "..." }
          directlyAsked: { rush_processing: true }
        }
      });

      // Shipment details fragment (priority 2)
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 2
      });
    } catch (error) {
      this.logger.error(
        `Failed to process rush processing request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }

    return fragments;
  }
}
