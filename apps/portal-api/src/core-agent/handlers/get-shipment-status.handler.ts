import { Injectable, Inject, Logger } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { AnswerUserQueryService } from "../services/answer-user-query.service";
import { ShipmentResponseService } from "../services/shipment-response.service";
import { ClassifyQuestionCategoryOutput } from "../schemas/classify-question-category.schema";
import { QUESTION_CATEGORIES } from "../constants/question-categories.constants";

/**
 * Handles shipment status inquiries and specific questions.
 * Classifies questions and generates appropriate response fragments.
 */
@Injectable()
export class GetShipmentStatusHandler extends BaseIntentHandler {
  constructor(
    @Inject(AnswerUserQueryService) private readonly answerUserQueryService: AnswerUserQueryService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "GET_SHIPMENT_STATUS" as const,
    description: "Checks and inquiries on shipment status, including customs processing or clearance status",
    examples: [
      "Where's my load CCN123456789 at? Did it clear customs yet?",
      "Any update on HBL987? What's the hold up?",
      "What's the current status of my shipment?",
      "Has my cargo been released?",
      "When will customs clear this?",
      "What is the transaction number?",
      "When will it arrive?",
      "Are any documents missing?"
    ],
    keywords: [
      "status",
      "update",
      "where",
      "customs",
      "clearance",
      "released",
      "cleared",
      "progress",
      "eta",
      "arrival",
      "missing",
      "documents",
      "transaction"
    ]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling GET_SHIPMENT_STATUS for shipment ${context.shipment.id} with ${instructions.length} questions`
    );

    // Step 1: Classify the user's questions using LLM
    const classifications = await this.classifyQuestions(instructions);
    this.logger.debug(`Question classifications: ${JSON.stringify([...classifications])}`);

    // Step 2: Handle specific non-customs-status questions first
    await this.addSpecificQuestionFragments(fragments, classifications, context, instructions);

    // Step 3: Add customs status response if appropriate
    const shouldAddCustomsStatus = this.shouldAddCustomsStatusResponse(classifications);
    if (shouldAddCustomsStatus) {
      // Status message fragment
      fragments.push({
        template: "core-agent/fragments/status-message",
        priority: 10, // Lower priority than specific answers
        fragmentContext: this.shipmentResponseService.buildComplianceDetails(context)
      });

      // Shipment details fragment
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 11
      });
    }

    // Mark status as directly asked
    // Mark as directly asked - now handled via fragmentContext
    // this.markAsDirectlyAsked(context, "shipment_status");

    return fragments;
  }

  /**
   * Classify user questions using the LLM service
   */
  private async classifyQuestions(questions: string[]): Promise<Set<string>> {
    const classifications = new Set<string>();

    if (questions.length === 0) {
      // Default to general status if no specific questions
      classifications.add(QUESTION_CATEGORIES.GENERAL_STATUS.category);
      return classifications;
    }

    try {
      // Classify each question
      const classificationPromises = questions.map((q) => this.answerUserQueryService.classifyQuestion(q));
      const results = await Promise.all(classificationPromises);

      // Collect unique categories
      results.forEach((result: ClassifyQuestionCategoryOutput) => {
        if (result && result.category) {
          classifications.add(result.category);
        }
      });
    } catch (error: any) {
      this.logger.error(`Error classifying questions: ${error.message}`, error.stack);
      // Fallback to general status
      classifications.add(QUESTION_CATEGORIES.GENERAL_STATUS.category);
    }

    return classifications;
  }

  /**
   * Add specific question response fragments for non-customs-status questions
   */
  private async addSpecificQuestionFragments(
    fragments: ResponseFragment[],
    classifications: Set<string>,
    context: ShipmentContext,
    questions: string[]
  ): Promise<void> {
    let fragmentPriority = 1; // Start with highest priority

    // Handle ETA questions
    if (classifications.has(QUESTION_CATEGORIES.ETA.category)) {
      const etaAnswer = await this.generateEtaAnswer(context, questions);
      if (etaAnswer) {
        fragments.push({
          template: "answer-eta-template",
          priority: fragmentPriority++,
          fragmentContext: { answer: etaAnswer }
        });
      }
    }

    // Handle Transaction Number questions
    if (classifications.has(QUESTION_CATEGORIES.TRANSACTION_NUMBER.category)) {
      const transactionAnswer = this.generateTransactionNumberAnswer(context);
      if (transactionAnswer) {
        fragments.push({
          template: "answer-transaction-number-template",
          priority: fragmentPriority++,
          fragmentContext: { answer: transactionAnswer }
        });
      }
    }

    // Handle Release Status questions
    if (classifications.has(QUESTION_CATEGORIES.RELEASE_STATUS.category)) {
      const releaseAnswer = this.generateReleaseStatusAnswer(context);
      if (releaseAnswer) {
        fragments.push({
          template: "answer-release-status-template",
          priority: fragmentPriority++,
          fragmentContext: { answer: releaseAnswer }
        });
      }
    }

    // Handle Shipping Status questions
    if (classifications.has(QUESTION_CATEGORIES.SHIPPING_STATUS.category)) {
      const shippingAnswer = this.generateShippingStatusAnswer(context);
      if (shippingAnswer) {
        fragments.push({
          template: "answer-shipping-status-template",
          priority: fragmentPriority++,
          fragmentContext: { answer: shippingAnswer }
        });
      }
    }
  }

  /**
   * Determine if customs status response should be added
   */
  private shouldAddCustomsStatusResponse(classifications: Set<string>): boolean {
    return (
      classifications.has(QUESTION_CATEGORIES.GENERAL_STATUS.category) ||
      classifications.has(QUESTION_CATEGORIES.CUSTOMS_STATUS.category) ||
      classifications.has(QUESTION_CATEGORIES.SHIPMENT_ISSUES.category) ||
      classifications.has(QUESTION_CATEGORIES.RELEASE_STATUS.category) ||
      classifications.size === 0 // Fallback for no classifications
    );
  }

  /**
   * Generate ETA-specific answer
   */
  private async generateEtaAnswer(context: ShipmentContext, questions: string[]): Promise<string | null> {
    const { smartTemplateContext } = context;

    if (smartTemplateContext?.hasETA && smartTemplateContext?.etaDate) {
      return `The estimated time of arrival (ETA) is ${smartTemplateContext.etaDate}.`;
    } else if (context.shipment?.etaPort) {
      return `The estimated time of arrival (ETA) is ${context.shipment.etaPort}.`;
    } else {
      return "The ETA is not yet available. We will update you once we receive this information from the carrier.";
    }
  }

  /**
   * Generate Transaction Number answer
   */
  private generateTransactionNumberAnswer(context: ShipmentContext): string | null {
    if (context.shipment?.transactionNumber) {
      return `The transaction number is ${context.shipment.transactionNumber}.`;
    } else {
      return "The transaction number is not yet available. It will be provided once the entry is submitted to customs.";
    }
  }

  /**
   * Generate Release Status answer
   */
  private generateReleaseStatusAnswer(context: ShipmentContext): string | null {
    const { shipment, smartTemplateContext } = context;

    if (shipment?.customsStatus === "released") {
      if (smartTemplateContext?.formattedReleaseDate && smartTemplateContext.formattedReleaseDate !== "TBD") {
        return `Yes, your shipment has been released by CBSA on ${smartTemplateContext.formattedReleaseDate}.`;
      } else if (shipment.releaseDate) {
        return `Yes, your shipment has been released by CBSA on ${shipment.releaseDate}.`;
      } else {
        return "Yes, your shipment has been released by CBSA.";
      }
    } else {
      // Use dynamic status information instead of hardcoded string
      const statusMessage =
        smartTemplateContext?.statusResponseMessage ||
        context.formattedCustomsStatus ||
        "Status information is not available";

      return `Your shipment has not yet been released by customs. Current status: ${statusMessage}. We will notify you as soon as it is cleared.`;
    }
  }

  /**
   * Generate Shipping Status answer
   */
  private generateShippingStatusAnswer(context: ShipmentContext): string | null {
    const { shipment } = context;

    if (shipment?.trackingStatus) {
      return `The shipping status is: ${shipment.trackingStatus.toLowerCase().replace(/_/g, " ")}.`;
    } else {
      return "Shipping status information is not currently available.";
    }
  }

  /**
   * Format missing fields for display in status messages
   */
  private formatMissingFields(context: ShipmentContext): string {
    const formattedFields = context.missingFieldsAnalysis?.formattedMissingFields || [];
    return formattedFields.join("\n");
  }
}
