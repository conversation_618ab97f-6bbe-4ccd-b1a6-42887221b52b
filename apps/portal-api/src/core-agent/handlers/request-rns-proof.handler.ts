import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { Shipment } from "nest-modules";
import { RnsProofService } from "../../email/services/rns-proof-service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * <PERSON><PERSON> requests for RNS (Release Notification System) proof of release documents.
 * Evaluates business rules and generates RNS proof documents when possible.
 */
@Injectable()
export class RequestRNSProofHandler extends BaseIntentHandler {
  constructor(
    private readonly rnsProofService: RnsProofService,
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RNS_PROOF" as const,
    description: "User is requesting RNS proof of release or release notification document",
    examples: [
      "Can you send me the RNS proof of release?",
      "I need the release notification",
      "Please provide proof of release",
      "Send me the RNS document",
      "Do you have the release paperwork?"
    ],
    keywords: ["rns", "proof of release", "release notification", "release document", "release paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const fragments: ResponseFragment[] = [];

    this.logger.log(`Handling REQUEST_RNS_PROOF for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate RNS proof: no shipment found in context");
      throw new Error("No shipment found for RNS proof generation");
    }

    try {
      // Collect data from helper methods (no context mutation)
      const rnsData = context.smartTemplateContext.rnsDocumentAvailable
        ? await this.generateRNSProofContent(
            context.shipment,
            context.smartTemplateContext,
            context.organization.id
          )
        : {};

      // RNS response fragment (priority 1)
      fragments.push({
        template: "core-agent/fragments/send-rns-response",
        priority: 1,
        fragmentContext: {
          ...rnsData, // { rnsProofData: {...}, rnsProofContent: "..." }
          ...this.shipmentResponseService.buildComplianceDetails(context),
          directlyAsked: { rns_proof: true }
        }
      });

      // Shipment details fragment (priority 2)
      fragments.push({
        template: "core-agent/fragments/details",
        priority: 2
      });
    } catch (error) {
      this.logger.error(
        `Failed to generate RNS proof of release for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }

    return fragments;
  }

  /**
   * Generate RNS proof content and return as fragment context data
   */
  private async generateRNSProofContent(
    shipment: Shipment,
    smartTemplateContext: any,
    organizationId: number
  ): Promise<Partial<FragmentContext>> {
    // Use the RnsProofService to get the RNS data
    const rnsProofData = await this.rnsProofService.getRNSProofOfRelease(shipment);

    if (!rnsProofData.isReleased || !rnsProofData.rnsResponse) {
      throw new Error("No release information found for this shipment");
    }

    // Get organization importer
    const importersResponse = await this.importerService.getImporters({
      organizationId: organizationId,
      limit: 1
    });
    const organizationImporter =
      importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

    // Use the existing prepareRNSStatusEmail method to generate the same email content
    const rnsEmailDto = this.rnsStatusChangeEmailSender.prepareRNSStatusEmail(
      shipment,
      organizationImporter,
      rnsProofData.rnsResponse,
      null, // no previous email for this context
      [] // no recipients needed for this context
    );

    // Format the email text as HTML for the response
    const rnsProofContent = `
      <div style="font-family: monospace; white-space: pre-line; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px;">
        <strong>RNS PROOF OF RELEASE</strong>

        ${rnsEmailDto.text}
      </div>
    `.trim();

    this.logger.log(`Successfully generated RNS proof of release for shipment ${shipment.id}.`);

    return {
      rnsProofData: {
        content: rnsProofContent,
        releaseDate: rnsProofData.releaseDate
      },
      rnsProofContent: rnsProofContent
    };
  }
}
