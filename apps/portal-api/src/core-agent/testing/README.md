# Core Agent Intent Handler Testing

This directory contains comprehensive testing scripts for the Core Agent intent handler system. These scripts allow you to test all 9 intent handlers individually or together using real shipment data from your database.

## Directory Structure

```
apps/portal-api/src/core-agent/testing/
├── README.md                           # This file
├── test-intent-handlers.js             # Main testing script
├── test-processor-direct.js            # NEW: Direct processor testing
├── run-processor-test-with-logs.sh     # NEW: Processor test with logging
├── find-test-shipments.js             # Shipment discovery helper
├── e2e-email-pipeline-nestjs.js        # End-to-end email pipeline testing
├── run-e2e-with-logs.sh               # E2E test with logging
└── [other utility scripts...]
```

## Overview

The refactored email system uses 9 intent handlers that process different types of email requests. These scripts provide comprehensive testing capabilities for the entire intent handler system, including direct testing of the processor logic that orchestrates the handlers.

## Available Scripts

### 1. `test-intent-handlers.js` - Main Testing Script

Tests all 9 intent handlers with real shipment data and shows their outputs.

**Basic Usage:**
```bash
# Navigate to the portal-api directory first
cd apps/portal-api

# Test all intents with demo organization
node src/core-agent/testing/test-intent-handlers.js

# Test specific intent
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_CAD_DOCUMENT

# Test with specific shipment
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --verbose

# Test without side effects (no emails sent)
node src/core-agent/testing/test-intent-handlers.js --no-side-effects
```

**Options:**
- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--no-side-effects` - Skip side effects like backoffice emails
- `--verbose` - Show detailed logging and fragment details

### 2. `test-processor-direct.js` - **NEW** Direct Processor Testing

Tests the `handle-request-message.processor.ts` logic directly by feeding different intents and shipment data to see what templates and output they produce. This bypasses the full email pipeline and focuses on testing the processor stage specifically.

**Basic Usage:**
```bash
# Navigate to the portal-api directory first
cd apps/portal-api

# Run processor test with logging
./src/core-agent/testing/run-processor-test-with-logs.sh

# Test specific intent with auto-selected shipment
node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS --verbose

# Test with custom instructions
node src/core-agent/testing/test-processor-direct.js --intent=REQUEST_CAD_DOCUMENT --instructions="Please send me the CAD document for my shipment"

# Test all intents for a specific shipment with HTML output
node src/core-agent/testing/test-processor-direct.js --shipment=123 --verbose --show-html

# Test with specific organization
node src/core-agent/testing/test-processor-direct.js --org=1 --intent=PROCESS_DOCUMENT --verbose
```

**Options:**
- `--org=ID` - Organization ID (default: 3 - demo org)
- `--shipment=ID` - Specific shipment ID to test with
- `--intent=INTENT_NAME` - Test only specific intent
- `--instructions="text"` - Custom instructions for the intent
- `--verbose` - Show detailed logging including fragment details
- `--show-html` - Show the full rendered HTML output
- `--no-side-effects` - Skip side effects like backoffice emails

**Key Features:**
- Tests the exact same logic as the processor uses (intent handling, fragment generation, HTML rendering)
- Shows detailed fragment information and side effects
- Can display full HTML output or just previews
- Supports custom instructions for testing different scenarios
- Bypasses email pipeline for focused processor testing

### 3. `find-test-shipments.js` - Shipment Discovery Script

Helps find suitable shipments for testing different scenarios.

**Basic Usage:**
```bash
# Find diverse shipments for testing
node src/core-agent/testing/find-test-shipments.js

# Show analysis of all customs statuses
node src/core-agent/testing/find-test-shipments.js --analysis

# Find shipments with specific status
node src/core-agent/testing/find-test-shipments.js --status=released
```

**Options:**
- `--org=ID` - Organization ID (default: 3)
- `--status=STATUS` - Filter by customs status
- `--limit=N` - Number of shipments to show (default: 10)
- `--analysis` - Show detailed analysis by status

### 4. `db-query.js` - Database Query Helper

Existing script for querying emails and shipments.

```bash
# List organizations
node scripts/db-query.js orgs

# Execute custom SQL
node scripts/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
```

**Note:** The `db-query.js` script remains in the main `scripts/` directory as it's used across multiple modules.

### Template Compilation Requirements

**CRITICAL**: New templates must be compiled before testing:

```bash
# Always run build after creating new templates
cd apps/portal-api
rushx build

# Or from project root
rush build --to portal-api

# Verify templates exist in dist directory
ls -la dist/core-agent/templates/
```

**Issue**: Templates created in `src/` are not automatically available until compiled to `dist/`.
**Solution**: Always run `rushx build` or `rush build --to portal-api` after creating new template files.

## Intent Handlers Tested

| Intent | Description | Side Effects | Requirements |
|--------|-------------|--------------|--------------|
| `GET_SHIPMENT_STATUS` | Status inquiries | CoreAgent answers | Shipment |
| `PROCESS_DOCUMENT` | Document processing | Entry submission | Shipment |
| `REQUEST_RUSH_PROCESSING` | Rush requests | Backoffice alerts | None |
| `DOCUMENTATION_COMING` | Acknowledge incoming docs | None | None |
| `UPDATE_SHIPMENT` | Update shipment info | Field updates | Shipment |
| `REQUEST_CAD_DOCUMENT` | CAD document requests | Document generation | Shipment |
| `REQUEST_RNS_PROOF` | RNS proof requests | Document generation | Shipment |
| `REQUEST_MANUAL_PROCESSING` | Manual processing | Backoffice alerts | None |
| `REQUEST_HOLD_SHIPMENT` | Hold processing | Backoffice alerts | None |

## Testing Workflow

### Step 1: Find Suitable Shipments

```bash
# Get overview of available shipments
node src/core-agent/testing/find-test-shipments.js --analysis

# Find diverse shipments for comprehensive testing
node src/core-agent/testing/find-test-shipments.js
```

### Step 2: Test All Handlers

```bash
# Test all handlers with auto-selected shipment
node src/core-agent/testing/test-intent-handlers.js --verbose

# Test with specific shipment from Step 1
node src/core-agent/testing/test-intent-handlers.js --shipment=456 --verbose
```

### Step 3: Test Specific Scenarios

```bash
# Test CAD document generation (requires released shipment)
node src/core-agent/testing/find-test-shipments.js --status=released
node src/core-agent/testing/test-intent-handlers.js --shipment=789 --intent=REQUEST_CAD_DOCUMENT

# Test RNS proof generation (requires released shipment)
node src/core-agent/testing/test-intent-handlers.js --shipment=789 --intent=REQUEST_RNS_PROOF

# Test document processing (requires shipment with pending status)
node src/core-agent/testing/find-test-shipments.js --status=live
node src/core-agent/testing/test-intent-handlers.js --shipment=123 --intent=PROCESS_DOCUMENT
```

### Step 4: Test Side Effects

```bash
# Test backoffice alerts (check your email)
node src/core-agent/testing/test-intent-handlers.js --intent=REQUEST_RUSH_PROCESSING

# Test without side effects for development
node src/core-agent/testing/test-intent-handlers.js --no-side-effects
```

## Understanding Test Output

### Success Output
```
🧪 Testing Intent: REQUEST_CAD_DOCUMENT
📝 Description: Request CAD document
📋 Instructions: Can you send me the CAD document?, Please provide the customs...
⚙️  Processing intent with handler...
✅ Handler completed in 245ms
📊 Generated 1 response fragments
🎨 Rendering fragments to HTML...
🔧 Side Effects Generated:
   - cadDocument: object
📧 Response Preview: Please see CAD document attached...
```

### Error Output
```
🧪 Testing Intent: REQUEST_CAD_DOCUMENT
❌ Error testing intent REQUEST_CAD_DOCUMENT: Shipment not ready for CAD generation
```

### Summary Output
```
📊 Test Results Summary
======================
✅ PASS GET_SHIPMENT_STATUS
✅ PASS REQUEST_CAD_DOCUMENT
❌ FAIL REQUEST_RNS_PROOF
     Error: No RNS data available

📈 Overall Results:
   Tests Passed: 8/9
   Total Fragments: 12
   Total Processing Time: 1,234ms
   Average Time per Intent: 154ms
```

## Customs Status Recommendations

Different customs statuses are better for testing different intents:

- **`released`** - Best for all document requests (CAD, RNS)
- **`live`** - Good for document processing and CAD requests
- **`pending-confirmation`** - Good for status inquiries and updates
- **`pending-commercial-invoice`** - Good for status inquiries and rush requests

## Troubleshooting

### Common Issues

1. **No shipments found**
   ```bash
   # Check if organization has shipments
   node scripts/db-query.js sql "SELECT COUNT(*) FROM shipment WHERE \"organizationId\" = 3"
   
   # Try different organization
   node src/core-agent/testing/find-test-shipments.js --org=1
   ```

2. **Handler not found**
   ```bash
   # Check available intents
   node src/core-agent/testing/test-intent-handlers.js
   ```

3. **Side effects not working**
   ```bash
   # Test without side effects first
   node src/core-agent/testing/test-intent-handlers.js --no-side-effects

   # Check logs for specific errors
   node src/core-agent/testing/test-intent-handlers.js --verbose
   ```

4. **CAD/RNS generation fails**
   ```bash
   # Find shipments with released status
   node src/core-agent/testing/find-test-shipments.js --status=released

   # Test with released shipment
   node src/core-agent/testing/test-intent-handlers.js --shipment=ID --intent=REQUEST_CAD_DOCUMENT
   ```

## Development Tips

- Use `--no-side-effects` during development to avoid sending emails
- Use `--verbose` to see detailed fragment and context information
- Test with different customs statuses to verify business logic
- Check the database after tests to verify side effects worked
- Use specific shipment IDs for consistent testing

## Integration with E2E Tests

These scripts complement the existing e2e email pipeline tests:

```bash
# Run full e2e pipeline test
node scripts/e2e-email-pipeline-nestjs.js

# Test specific intent handlers
node src/core-agent/testing/test-intent-handlers.js

# Find shipments for manual testing
node src/core-agent/testing/find-test-shipments.js
```

The intent handler tests focus on individual handler logic, while e2e tests verify the complete email processing pipeline.
